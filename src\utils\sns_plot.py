import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
from pathlib import Path

import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from typing import Union, List, Tuple, Optional, Dict


class SeabornVisualizer:
    """
    强大的Seaborn可视化工具类
    支持多种图表类型和深度自定义

    功能特点：
    - 内置专业美观的默认样式
    - 支持折线图、柱状图、箱线图、小提琴图、热力图等
    - 自动颜色主题管理
    - 智能标注和统计信息添加
    - 多子图布局支持
    - 图表元素精细控制

    使用示例：
    >>> viz = SeabornVisualizer()
    >>> viz.lineplot(data=df, x='date', y='sales', hue='region')
    >>> viz.show()
    """

    def __init__(
        self,
        style: str = "whitegrid",
        palette: str = "muted",
        context: str = "notebook",
        font_scale: float = 1.1,
    ):
        """
        初始化可视化工具

        :param style: Seaborn样式 (darkgrid, whitegrid, dark, white, ticks)
        :param palette: 颜色主题 (deep, muted, bright, pastel, dark, colorblind)
        :param context: 绘图上下文 (paper, notebook, talk, poster)
        :param font_scale: 字体缩放比例
        """
        self.set_style(style, palette, context, font_scale)
        self.fig = None
        self.ax = None

    def set_style(self, style: str, palette: str, context: str, font_scale: float):
        """设置绘图样式"""
        sns.set_theme(
            style=style, palette=palette, context=context, font_scale=font_scale
        )
        self._set_custom_rc_params()

    def _set_custom_rc_params(self):
        """设置增强的RC参数"""
        plt.rcParams.update(
            {
                "figure.dpi": 120,
                "savefig.dpi": 300,
                "axes.titleweight": "bold",
                "axes.titlelocation": "left",
                "axes.titlesize": 14,
                "axes.titlepad": 12,
                "axes.spines.top": False,
                "axes.spines.right": False,
                "grid.alpha": 0.15,
                "legend.frameon": True,
                "legend.edgecolor": "none",
                "legend.fancybox": True,
                "legend.fontsize": 11,
            }
        )

    def create_figure(self, figsize: Tuple[int, int] = (10, 6)):
        """创建新的图形"""
        self.fig, self.ax = plt.subplots(figsize=figsize)
        return self.ax

    def apply_color_palette(self, palette: str, n_colors: int = 6):
        """应用新的颜色主题"""
        sns.set_palette(palette, n_colors)

    def _process_plot_params(self, **kwargs):
        """处理通用绘图参数"""
        title = kwargs.pop("title", None)
        xlabel = kwargs.pop("xlabel", None)
        ylabel = kwargs.pop("ylabel", None)
        legend = kwargs.pop("legend", True)

        if title:
            self.ax.set_title(title, fontsize=14, pad=12)
        if xlabel:
            self.ax.set_xlabel(xlabel, fontsize=12)
        if ylabel:
            self.ax.set_ylabel(ylabel, fontsize=12)
        if legend and self.ax.get_legend() is not None:
            self.ax.legend(loc="best", framealpha=0.8)

        # 自动旋转x轴标签
        if self.ax.get_xticklabels():
            rotation = kwargs.pop(
                "x_rotation", 45 if len(self.ax.get_xticklabels()) > 5 else 0
            )
            plt.setp(
                self.ax.get_xticklabels(),
                rotation=rotation,
                ha="right" if rotation else "center",
            )

        # 添加网格线
        if kwargs.pop("grid", True):
            self.ax.grid(alpha=0.15, linestyle="--")

    def show(self):
        """显示图表"""
        if self.fig:
            plt.tight_layout()
            plt.show()

    def save(
        self,
        filename: str,
        dpi: int = 300,
        transparent: bool = False,
        bbox_inches: str = "tight",
    ):
        """
        保存图表到文件

        :param filename: 文件名 (带扩展名)
        :param dpi: 分辨率
        :param transparent: 是否透明背景
        :param bbox_inches: 边界框设置
        """
        if self.fig:
            self.fig.savefig(
                filename, dpi=dpi, transparent=transparent, bbox_inches=bbox_inches
            )
            print(f"图表已保存至: {filename}")
